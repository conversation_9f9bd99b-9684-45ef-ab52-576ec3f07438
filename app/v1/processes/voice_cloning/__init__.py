from PIL import Image, ImageDraw
import json
import asyncio
from typing import List, Tuple, Union, Optional, Dict, Any
from bson import ObjectId
from io import BytesIO
import httpx
import os
from pathlib import Path

from app.models.user import UserTenantDB
from app.v1.api.jobs.models import JobStatus
from app.core.helper.logger import setup_new_logging
from fastapi.responses import JSONResponse

from datetime import datetime

logger = setup_new_logging(__name__)

class VoiceCloning:
    """
    A class for voice cloning and timbre transfer using local model 
    """
    def __init__(self, user_tenant_info: UserTenantDB):
        self.user_tenant_info = user_tenant_info
        self.api_url = "https://tts-api.nextai.asia/v2/speak_v2"
        self.headers = {
            'accept': 'application/json',
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyZGJhZWQ0YzVhNmZkNmQ3NjUwNzcwIiwiZXhwIjoxNzg2MDc5OTQzfQ.fdKa2prk_F__AQjRpJ6qIKh7YzU5r-SnXJIlU04C0Hc',
            'Content-Type': 'application/x-www-form-urlencoded',
        }

    async def process_voice_cloning(self, media_id: str) -> Dict[str, Any]:
        """
        Process voice cloning for a single media item.
        
        Args:
            media_id: ID of the media containing voice cloning data
            
        Returns:
            Result from voice cloning processing
        """
        try:
            media_collection = self.user_tenant_info.async_db.media
            media = await media_collection.find_one({"_id": ObjectId(media_id)})

            if not media:
                raise ValueError(f"Media not found: {media_id}")

            # Debug: Log media record details
            logger.info(f"Media record: filename={media.get('filename')}, content_type={media.get('content_type')}, size={media.get('size')}")
            logger.info(f"Media bucket_name={media.get('bucket_name')}, object_name={media.get('object_name')}")
            
            # Get the file from MinIO
            loop = asyncio.get_event_loop()
            file_data = await loop.run_in_executor(
                None,
                self.user_tenant_info.minio_client.get_object,
                media["bucket_name"],
                media["object_name"]
            )

            # Load entries from the file content
            file_content_bytes = await loop.run_in_executor(None, file_data.read)
            file_content = file_content_bytes.decode('utf-8')
            logger.info(f"File content length: {len(file_content)}, filename: {media['filename']}")
            logger.info(f"File content preview: '{file_content[:200]}...'")
            logger.info(f"File content repr: {repr(file_content[:200])}")
            entries = self._parse_file_content(file_content, media["filename"])
            
            logger.info(f"Processing {len(entries)} voice cloning entries for media {media_id}")
            
            # Process all entries
            results = []
            for i, entry in enumerate(entries):
                try:
                    validated_entry = self._validate_entry(entry, i + 1)
                    result = await self._process_single_entry(validated_entry, media_id)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error processing entry {i + 1}: {e}")
                    results.append({
                        "error": str(e),
                        "entry_index": i + 1,
                        "status": "failed",
                        "entry": entry
                    })
            
            successful_count = len([r for r in results if r.get('status') == 'success'])
            logger.info(f"Voice cloning completed for media {media_id}. "
                       f"Processed {len(results)} entries, {successful_count} successful")
            
            return {
                "media_id": media_id,
                "result": {
                    "data": results,
                    "total_entries": len(entries),
                    "successful_entries": successful_count,
                    "failed_entries": len(results) - successful_count
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing voice cloning for media {media_id}: {e}")
            return {
                "media_id": media_id,
                "error": str(e)
            }
    
    async def process_multiple_voice_cloning(self, media_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Process voice cloning for multiple media items in parallel.
        
        Args:
            media_ids: List of media IDs to process
            
        Returns:
            List of results from voice cloning processing
        """
        tasks = [self.process_voice_cloning(media_id) for media_id in media_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception processing media {media_ids[i]}: {result}")
                processed_results.append({
                    "media_id": media_ids[i],
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results

    def _parse_file_content(self, file_content: str, filename: str) -> List[Dict[str, Any]]:
        """
        Parse file content to extract voice cloning entries.

        Args:
            file_content: Content of the file as string
            filename: Name of the file for format detection

        Returns:
            List of entries with required fields
        """
        entries = []
        file_path_obj = Path(filename)

        logger.info(f"Parsing file: {filename}, extension: {file_path_obj.suffix.lower()}")
        logger.info(f"File content length: {len(file_content)}")
        logger.info(f"File content stripped length: {len(file_content.strip())}")
        logger.info(f"File content repr (first 300 chars): {repr(file_content[:300])}")

        # Check if file content is empty
        if not file_content.strip():
            raise ValueError(f"File {filename} is empty or contains only whitespace")

        if file_path_obj.suffix.lower() == '.jsonl':
            # Handle JSONL format (one JSON object per line)
            logger.info(f"Parsing JSONL file: {filename}")
            lines = file_content.strip().split('\n')
            logger.info(f"Number of lines after split: {len(lines)}")
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                logger.info(f"Processing line {line_num}: '{line}' (length: {len(line)})")
                if line:
                    try:
                        entry = json.loads(line)
                        entries.append(entry)
                        logger.info(f"Successfully parsed entry {line_num}: {entry}")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON on line {line_num} in {filename}: {e}")
                        continue
        else:
            # Handle JSON format (array of objects or single object)
            logger.info(f"Parsing JSON file: {filename}")
            try:
                data = json.loads(file_content)
                if isinstance(data, list):
                    entries.extend(data)
                else:
                    entries.append(data)
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON file {filename}: {e}. File content preview: {file_content[:100]}...")

        if not entries:
            raise ValueError(f"No valid entries found in file {filename}")

        return entries

    def _validate_entry(self, entry: Dict[str, Any], entry_index: int) -> Dict[str, Any]:
        """
        Validate and normalize an entry.
        
        Args:
            entry: The entry to validate
            entry_index: Index for error reporting
            
        Returns:
            Validated entry
        """
        if not isinstance(entry, dict):
            raise ValueError(f"Entry {entry_index} must be a dictionary")
        
        # Required fields
        required_fields = ['text', 'preset_voice', 'user_voice_id']
        for field in required_fields:
            if field not in entry:
                raise ValueError(f"Entry {entry_index} missing required field: {field}")
            if not entry[field]:
                raise ValueError(f"Entry {entry_index} has empty required field: {field}")
        
        # Optional quality field with default
        if 'quality' not in entry:
            entry['quality'] = '20'  # Default quality
        
        return entry

    async def extract_from_job(self, job_id: str) -> JSONResponse:
        """
        Extract voice cloning data from all media items in a job in parallel.
        
        Args:
            job_id: Job ID to process
            
        Returns:
            JSONResponse with processing results
        """
        jobs_collection = self.user_tenant_info.async_db.jobs
        job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
        
        if not job or not job.get("items"):
            raise ValueError(f"Invalid job or no items found for job {job_id}")

        # Update job status to processing
        await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.utcnow()}}
        )

        # Get media IDs from items
        media_ids = []
        process_items = {}  # Map to track process items by media_id

        for item in job["items"]:
            if item.get("input_id"):
                media_id = str(item["input_id"])
                media_ids.append(media_id)
                process_items[media_id] = item
        
        try:
            # Process all media items in parallel
            results = await self.process_multiple_voice_cloning(media_ids)
            
            media_collection = self.user_tenant_info.async_db.media

            for result in results:
                media_id = result["media_id"]
                process_item = process_items[media_id]

                if "error" in result:
                    # Update process item status to failed
                    process_item["status"] = JobStatus.FAILED
                    process_item["error"] = result["error"]
                else:
                    # Create output media document
                    output_media = {
                        "filename": f"voice_cloning_output_{media_id}.json",
                        "content_type": "application/json",
                        "bucket_name": self.user_tenant_info.minio_bucket_name,
                        "object_name": f"{job_id}/output/voice_cloning_{media_id}.json",
                        "job_id": ObjectId(job_id),
                        "created_at": datetime.utcnow(),
                        "created_by": job["created_by"],
                        "metadata": {
                            "source_media_id": ObjectId(media_id),
                            "process_type": "voice-cloning",
                            "type": "output"
                        }
                    }

                    # Insert output media
                    output_result = await media_collection.insert_one(output_media)

                    # Update process item
                    process_item["output_id"] = output_result.inserted_id
                    process_item["status"] = JobStatus.COMPLETED

                    # Upload result to MinIO
                    result_data = result["result"].get("data", result["result"])
                    json_content = json.dumps(result_data, indent=2).encode('utf-8')
                    await asyncio.get_event_loop().run_in_executor(
                        None,
                        self.user_tenant_info.minio_client.put_object,
                        self.user_tenant_info.minio_bucket_name,
                        output_media["object_name"],
                        BytesIO(json_content),
                        len(json_content),
                        'application/json'
                    )
            
            # Update job status and items
            all_successful = all(item["status"] == JobStatus.COMPLETED for item in process_items.values())
            status = JobStatus.COMPLETED if all_successful else JobStatus.PARTIALLYCOMPLETED

            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": status,
                        "completed_at": datetime.utcnow(),
                        "error_count": sum(1 for item in process_items.values() if item["status"] == JobStatus.FAILED),
                        "items": list(process_items.values())
                    }
                }
            )
            
            logger.info(f"Voice cloning job {job_id} completed with status {status}")
            return JSONResponse(content=results)
            
        except Exception as e:
            # Update job status on failure
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            logger.error(f"Voice cloning job {job_id} failed: {e}")
            raise
    
    async def _process_single_entry(self, entry: Dict[str, Any], media_id: str) -> Dict[str, Any]:
        """
        Process a single voice cloning entry.
        
        Args:
            entry: The entry to process
            media_id: Media ID for tracking
            
        Returns:
            Result from the API call
        """
        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                response = await client.post(
                    self.api_url,
                    headers=self.headers,
                    data={
                        'text': entry['text'],
                        'preset_voice': entry['preset_voice'],
                        'user_voice_id': entry['user_voice_id'],
                        'quality': entry['quality']
                    }
                )
                
                response.raise_for_status()
                result = response.json()
                
                logger.info(f"Voice cloning successful for media {media_id}. "
                           f"Text: '{entry['text'][:50]}...', "
                           f"Voice ID: {entry['user_voice_id']}")
                
                return {
                    "status": "success",
                    "entry": entry,
                    "result": result,
                    "media_id": media_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error in voice cloning for media {media_id}: {e.response.status_code} - {e.response.text}")
            return {
                "status": "error",
                "entry": entry,
                "error": f"HTTP {e.response.status_code}: {e.response.text}",
                "media_id": media_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        except httpx.RequestError as e:
            logger.error(f"Request error in voice cloning for media {media_id}: {e}")
            return {
                "status": "error",
                "entry": entry,
                "error": f"Request error: {str(e)}",
                "media_id": media_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Unexpected error in voice cloning for media {media_id}: {e}")
            return {
                "status": "error",
                "entry": entry,
                "error": f"Unexpected error: {str(e)}",
                "media_id": media_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        