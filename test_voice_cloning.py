#!/usr/bin/env python3
"""
Test script for voice cloning API with JSONL file
"""
import requests
import json

# API Configuration
BASE_URL = "http://127.0.0.1:8000/v1"
USERNAME = "superadmin"
PASSWORD = "godmod"
TENANT = "test_tenant"

def authenticate():
    """Authenticate and get access token"""
    login_url = f"{BASE_URL}/login"
    
    data = {
        "grant_type": "password",
        "username": USERNAME,
        "password": PASSWORD,
        "scope": "",
        "client_id": TENANT
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    print("🔐 Authenticating...")
    response = requests.post(login_url, data=data, headers=headers)
    
    if response.status_code == 200:
        auth_data = response.json()
        print(f"✅ Authentication successful!")
        print(f"   User: {auth_data['username']}")
        print(f"   Role: {auth_data['role']}")
        print(f"   Tenant: {auth_data['tenant_label']}")
        return auth_data["access_token"]
    else:
        print(f"❌ Authentication failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def test_voice_cloning(token):
    """Test voice cloning with sample.jsonl file"""
    process_url = f"{BASE_URL}/processes/voice-cloning/run"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # Read the sample.jsonl file
    try:
        with open("sample.jsonl", "rb") as f:
            file_content = f.read()
            print(f"📄 File size: {len(file_content)} bytes")
            print(f"📄 File content preview: {file_content[:100]}...")
    except FileNotFoundError:
        print("❌ sample.jsonl file not found!")
        return
    
    files = {
        "files": ("sample.jsonl", open("sample.jsonl", "rb"), "application/octet-stream")
    }
    
    data = {
        "project_name": "voice-cloning-test",
        "job_name": "jsonl-test-api"
    }
    
    print("🎤 Testing voice cloning process...")
    response = requests.post(process_url, headers=headers, files=files, data=data)
    
    print(f"📊 Response status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Voice cloning process started successfully!")
        print(f"   Job ID: {result.get('job_id')}")
        print(f"   Status: {result.get('status')}")
        return result
    else:
        print(f"❌ Voice cloning failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def main():
    print("🚀 Starting Voice Cloning API Test")
    print("=" * 50)
    
    # Step 1: Authenticate
    token = authenticate()
    if not token:
        return
    
    print("\n" + "=" * 50)
    
    # Step 2: Test voice cloning
    result = test_voice_cloning(token)
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
